import java.util.Scanner;

public class StudentScoreManager {

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // Input: Student's Name
        System.out.print("Enter student's name: ");
        String studentName = scanner.nextLine();

        // Input: Scores for 5 Subjects
        double[] scores = new double[5];
        String[] subjects = {"Math", "Science", "English", "History", "Art"};

        System.out.println("\nEnter scores for 5 subjects:");
        for (int i = 0; i < 5; i++) {
            System.out.print(subjects[i] + ": ");
            scores[i] = scanner.nextDouble();
        }

        // Processing and Display Results
        System.out.println("\n" + "=".repeat(40));
        System.out.println("STUDENT SCORE REPORT");
        System.out.println("=".repeat(40));
        System.out.println("Student Name: " + studentName);
        System.out.println();

        // Display all 5 subject scores
        System.out.println("Subject Scores:");
        for (int i = 0; i < 5; i++) {
            System.out.printf("%-10s: %.2f\n", subjects[i], scores[i]);
        }

        // Calculate Total Score
        double totalScore = calculateTotal(scores);
        System.out.println("\nTotal Score: " + totalScore);

        // Calculate Average Score
        double averageScore = calculateAverage(scores);
        System.out.printf("Average Score: %.2f\n", averageScore);

        // Determine Grade using Average
        String grade = determineGrade(averageScore);
        System.out.println("Grade: " + grade);

        // Show Pass Status
        String passStatus = determinePassStatus(averageScore);
        System.out.println("Status: " + passStatus);

        scanner.close();
    }

    // Method to calculate total score
    public static double calculateTotal(double[] scores) {
        double total = 0;
        for (double score : scores) {
            total += score;
        }
        return total;
    }

    // Method to calculate average score
    public static double calculateAverage(double[] scores) {
        return calculateTotal(scores) / scores.length;
    }

    // Method to determine grade based on average score
    public static String determineGrade(double average) {
        if (average >= 90) {
            return "A";
        } else if (average >= 80) {
            return "B";
        } else if (average >= 70) {
            return "C";
        } else if (average >= 60) {
            return "D";
        } else if (average >= 50) {
            return "E";
        } else {
            return "F";
        }
    }

    // Method to determine pass status
    public static String determinePassStatus(double average) {
        if (average >= 50) {
            return "Passed";
        } else {
            return "Failed";
        }
    }
}
