import java.util.Scanner;

public class Circle {
    // Using more precise value of PI
    private static final double PI = 3.14159265359;

    // Clear screen method
    private static void clearScreen() {
        try {
            // Try to clear screen on Windows
            if (System.getProperty("os.name").contains("Windows")) {
                new ProcessBuilder("cmd", "/c", "cls").inheritIO().start().waitFor();
            } else {
                // Clear screen on Unix/Linux/Mac
                System.out.print("\033[2J\033[H");
                System.out.flush();
            }
        } catch (Exception e) {
            // If clearing fails, just print some empty lines
            for (int i = 0; i < 50; i++) {
                System.out.println();
            }
        }
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // Clear screen and show welcome message
        clearScreen();
        System.out.println("*** Welcome to Circle Area Calculator! ***");
        System.out.println("==========================================");

        while (true) {
            displayMenu();

            System.out.print("Enter your choice (1-4): ");
            int choice = scanner.nextInt();

            // Clear screen before showing calculation
            clearScreen();

            switch (choice) {
                case 1:
                    calculateUsingRadius(scanner);
                    break;
                case 2:
                    calculateUsingDiameter(scanner);
                    break;
                case 3:
                    calculateBothMethods(scanner);
                    break;
                case 4:
                    System.out.println("Thank you for using Circle Area Calculator!");
                    System.out.println("Goodbye!");
                    scanner.close();
                    return;
                default:
                    System.out.println("*** Invalid choice! Please select 1-4. ***");
                    // Don't clear screen for invalid input, let them see the error
                    continue;
            }

            // Wait for user to press Enter before showing menu again
            System.out.print("\n>>> Press Enter to continue...");
            scanner.nextLine(); // consume the newline
            scanner.nextLine(); // wait for user input
            clearScreen();
        }
    }

    private static void displayMenu() {
        System.out.println("*** Circle Area Calculator ***");
        System.out.println("==============================");
        System.out.println("\nChoose calculation method:");
        System.out.println("[1]  Calculate using Radius (A = πr²)");
        System.out.println("[2]  Calculate using Diameter (A = πd²/4)");
        System.out.println("[3]  Show both calculations");
        System.out.println("[4]  Exit");
        System.out.println("-".repeat(40));
    }

    private static void calculateUsingRadius(Scanner scanner) {
        System.out.println("*** Method 1: Using Radius Formula ***");
        System.out.println("======================================");
        System.out.println("Formula: A = πr²");
        System.out.println("Where π = " + PI);

        System.out.print("\nEnter the radius: ");
        double radius = scanner.nextDouble();

        if (radius < 0) {
            System.out.println("\n*** Error: Radius cannot be negative! ***");
            return;
        }

        double area = PI * radius * radius;

        System.out.println("\n--- Calculation Steps ---");
        System.out.println("A = π × r²");
        System.out.println("A = " + PI + " × " + radius + "²");
        System.out.println("A = " + PI + " × " + (radius * radius));
        System.out.println("A = " + String.format("%.2f", area) + " square units");

        System.out.println("\n*** Final Result ***");
        System.out.println(">>> Area = " + String.format("%.2f", area) + " square units <<<");
    }

    private static void calculateUsingDiameter(Scanner scanner) {
        System.out.println("*** Method 2: Using Diameter Formula ***");
        System.out.println("========================================");
        System.out.println("Formula: A = πd²/4");
        System.out.println("Where π = " + PI);

        System.out.print("\nEnter the diameter: ");
        double diameter = scanner.nextDouble();

        if (diameter < 0) {
            System.out.println("\n*** Error: Diameter cannot be negative! ***");
            return;
        }

        double area = (PI * diameter * diameter) / 4;

        System.out.println("\n--- Calculation Steps ---");
        System.out.println("A = π × d² / 4");
        System.out.println("A = " + PI + " × " + diameter + "² / 4");
        System.out.println("A = " + PI + " × " + (diameter * diameter) + " / 4");
        System.out.println("A = " + (PI * diameter * diameter) + " / 4");
        System.out.println("A = " + String.format("%.2f", area) + " square units");

        System.out.println("\n*** Final Result ***");
        System.out.println(">>> Area = " + String.format("%.2f", area) + " square units <<<");
    }

    private static void calculateBothMethods(Scanner scanner) {
        System.out.println("*** Both Methods Comparison ***");
        System.out.println("===============================");
        System.out.print("Enter the radius: ");
        double radius = scanner.nextDouble();

        if (radius < 0) {
            System.out.println("\n*** Error: Radius cannot be negative! ***");
            return;
        }

        double diameter = radius * 2;

        // Method 1: Using radius
        double area1 = PI * radius * radius;

        // Method 2: Using diameter
        double area2 = (PI * diameter * diameter) / 4;

        System.out.println("\n--- Method 1 - Using Radius ---");
        System.out.println("Formula: A = πr²");
        System.out.println("Given radius: " + radius);
        System.out.println("A = " + PI + " × " + radius + "²");
        System.out.println("A = " + String.format("%.2f", area1) + " square units");

        System.out.println("\n--- Method 2 - Using Diameter ---");
        System.out.println("Formula: A = πd²/4");
        System.out.println("Diameter = 2 × radius = 2 × " + radius + " = " + diameter);
        System.out.println("A = " + PI + " × " + diameter + "² / 4");
        System.out.println("A = " + String.format("%.2f", area2) + " square units");

        System.out.println("\n*** Verification ***");
        System.out.println("Method 1 result: " + String.format("%.2f", area1));
        System.out.println("Method 2 result: " + String.format("%.2f", area2));

        if (Math.abs(area1 - area2) < 0.001) {
            System.out.println(">>> Both methods give the same result! <<<");
        } else {
            System.out.println("Small difference due to rounding: " +
                    String.format("%.6f", Math.abs(area1 - area2)));
        }
    }
}