import java.util.Scanner;

public class RectangleCalculator {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== Rectangle Formula Calculator ===");
        System.out.println("This program calculates area and perimeter of rectangles");
        System.out.println();
        
        // Get number of rectangles to calculate
        System.out.print("How many rectangles do you want to calculate? ");
        int numRectangles = scanner.nextInt();
        
        // Arrays to store rectangle dimensions and results
        double[] lengths = new double[numRectangles];
        double[] widths = new double[numRectangles];
        double[] areas = new double[numRectangles];
        double[] perimeters = new double[numRectangles];
        
        // Input rectangle dimensions
        for (int i = 0; i < numRectangles; i++) {
            System.out.println();
            System.out.println("Rectangle " + (i + 1) + ":");
            System.out.print("Enter length: ");
            lengths[i] = scanner.nextDouble();
            System.out.print("Enter width: ");
            widths[i] = scanner.nextDouble();
            
            // Validate input
            if (lengths[i] <= 0 || widths[i] <= 0) {
                System.out.println("Error: Length and width must be positive numbers!");
                i--; // Repeat this iteration
                continue;
            }
            
            // Calculate area and perimeter
            areas[i] = calculateArea(lengths[i], widths[i]);
            perimeters[i] = calculatePerimeter(lengths[i], widths[i]);
        }
        
        // Display results
        System.out.println();
        System.out.println("=== CALCULATION RESULTS ===");
        System.out.println("Rectangle\tLength\t\tWidth\t\tArea\t\tPerimeter");
        System.out.println("------------------------------------------------------------------------");
        
        for (int i = 0; i < numRectangles; i++) {
            System.out.printf("%-9d\t%.2f\t\t%.2f\t\t%.2f\t\t%.2f%n", 
                (i + 1), lengths[i], widths[i], areas[i], perimeters[i]);
        }
        
        // Display summary statistics
        displaySummary(areas, perimeters);
        
        scanner.close();
    }
    
    /**
     * Calculates the area of a rectangle
     * Formula: Area = length × width
     */
    public static double calculateArea(double length, double width) {
        return length * width;
    }
    
    /**
     * Calculates the perimeter of a rectangle
     * Formula: Perimeter = 2 × (length + width)
     */
    public static double calculatePerimeter(double length, double width) {
        return 2 * (length + width);
    }
    
    /**
     * Displays summary statistics for all rectangles
     */
    public static void displaySummary(double[] areas, double[] perimeters) {
        System.out.println();
        System.out.println("=== SUMMARY STATISTICS ===");
        
        // Calculate totals
        double totalArea = 0;
        double totalPerimeter = 0;
        double maxArea = areas[0];
        double minArea = areas[0];
        double maxPerimeter = perimeters[0];
        double minPerimeter = perimeters[0];
        
        for (int i = 0; i < areas.length; i++) {
            totalArea += areas[i];
            totalPerimeter += perimeters[i];
            
            if (areas[i] > maxArea) maxArea = areas[i];
            if (areas[i] < minArea) minArea = areas[i];
            if (perimeters[i] > maxPerimeter) maxPerimeter = perimeters[i];
            if (perimeters[i] < minPerimeter) minPerimeter = perimeters[i];
        }
        
        // Calculate averages
        double avgArea = totalArea / areas.length;
        double avgPerimeter = totalPerimeter / perimeters.length;
        
        // Display summary
        System.out.printf("Total Area: %.2f%n", totalArea);
        System.out.printf("Total Perimeter: %.2f%n", totalPerimeter);
        System.out.printf("Average Area: %.2f%n", avgArea);
        System.out.printf("Average Perimeter: %.2f%n", avgPerimeter);
        System.out.printf("Largest Area: %.2f%n", maxArea);
        System.out.printf("Smallest Area: %.2f%n", minArea);
        System.out.printf("Largest Perimeter: %.2f%n", maxPerimeter);
        System.out.printf("Smallest Perimeter: %.2f%n", minPerimeter);
        
        System.out.println();
        System.out.println("Formulas used:");
        System.out.println("Area = length × width");
        System.out.println("Perimeter = 2 × (length + width)");
    }
}
