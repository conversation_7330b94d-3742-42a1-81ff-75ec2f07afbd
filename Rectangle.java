import java.util.Scanner;

public class Rectangle {
    
    public static void main(String[] args) {
        Scanner input = new Scanner(System.in);
        
        System.out.print("Rectangle Calculator\n");
        System.out.print("Enter length: ");
        double length = input.nextDouble();
        
        System.out.print("Enter width: ");
        double width = input.nextDouble();
        
        // calculate stuff
        double area = length * width;
        double perimeter = 2 * (length + width);
        
        System.out.print("\nResults:\n");
        System.out.print("Area = " + area + "\n");
        System.out.print("Perimeter = " + perimeter + "\n");
        
        // ask if they want to do another one
        System.out.print("\nDo another? (y/n): ");
        String answer = input.next();
        
        while (answer.equals("y") || answer.equals("Y")) {
            System.out.print("\nEnter length: ");
            length = input.nextDouble();
            
            System.out.print("Enter width: ");
            width = input.nextDouble();
            
            area = length * width;
            perimeter = 2 * (length + width);
            
            System.out.print("\nArea = " + area + "\n");
            System.out.print("Perimeter = " + perimeter + "\n");
            
            System.out.print("\nDo another? (y/n): ");
            answer = input.next();
        }
        
        System.out.print("Thanks for using the calculator!\n");
        input.close();
    }
}
